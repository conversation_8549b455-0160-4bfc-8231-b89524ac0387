function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ZhihuCircleFilledSvg from "@ant-design/icons-svg/es/asn/ZhihuCircleFilled";
import AntdIcon from "../components/AntdIcon";
const ZhihuCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ZhihuCircleFilledSvg
}));

/**![zhihu-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0tOTAuNyA0NzcuOGwtLjEgMS41Yy0xLjUgMjAuNC02LjMgNDMuOS0xMi45IDY3LjZsMjQtMTguMSA3MSA4MC43YzkuMiAzMy0zLjMgNjMuMS0zLjMgNjMuMWwtOTUuNy0xMTEuOXYtLjFjLTguOSAyOS0yMC4xIDU3LjMtMzMuMyA4NC43LTIyLjYgNDUuNy01NS4yIDU0LjctODkuNSA1Ny43LTM0LjQgMy0yMy4zLTUuMy0yMy4zLTUuMyA2OC01NS41IDc4LTg3LjggOTYuOC0xMjMuMSAxMS45LTIyLjMgMjAuNC02NC4zIDI1LjMtOTYuOEgyNjQuMXM0LjgtMzEuMiAxOS4yLTQxLjdoMTAxLjZjLjYtMTUuMy0xLjMtMTAyLjgtMi0xMzEuNGgtNDkuNGMtOS4yIDQ1LTQxIDU2LjctNDguMSA2MC4xLTcgMy40LTIzLjYgNy4xLTIxLjEgMCAyLjYtNy4xIDI3LTQ2LjIgNDMuMi0xMTAuNyAxNi4zLTY0LjYgNjMuOS02MiA2My45LTYyLTEyLjggMjIuNS0yMi40IDczLjYtMjIuNCA3My42aDE1OS43YzEwLjEgMCAxMC42IDM5IDEwLjYgMzloLTkwLjhjLS43IDIyLjctMi44IDgzLjgtNSAxMzEuNEg1MTlzMTIuMiAxNS40IDEyLjIgNDEuN0g0MjEuM3ptMzQ2LjUgMTY3aC04Ny42bC02OS41IDQ2LjYtMTYuNC00Ni42aC00MC4xVjMyMS41aDIxMy42djM4Ny4zek00MDguMiA2MTFzMC0uMSAwIDB6bTIxNiA5NC4zbDU2LjgtMzguMWg0NS42LS4xVjM2NC43SDU5Ni43djMwMi41aDE0LjF6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(ZhihuCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ZhihuCircleFilled';
}
export default RefIcon;