function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DropboxSquareFilledSvg from "@ant-design/icons-svg/es/asn/DropboxSquareFilled";
import AntdIcon from "../components/AntdIcon";
const DropboxSquareFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: DropboxSquareFilledSvg
}));

/**![dropbox-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjYzLjIgNjU5LjVMNTEyLjYgNzUwbC0xNTEtOTAuNXYtMzMuMWw0NS40IDI5LjQgMTA1LjYtODcuNyAxMDUuNiA4Ny43IDQ1LjEtMjkuNHYzMy4xem0tNDUuNi0yMi40bC0xMDUuMy04Ny43TDQwNyA2MzcuMWwtMTUxLTk5LjIgMTA0LjUtODIuNEwyNTYgMzcxLjIgNDA3IDI3NGwxMDUuMyA4Ny43TDYxNy42IDI3NCA3NjggMzcyLjFsLTEwNC4yIDgzLjVMNzY4IDUzOWwtMTUwLjQgOTguMXpNNTEyLjMgMzYxLjdsLTE1MS44IDkzLjggMTUxLjggOTMuOSAxNTEuNS05My45em0xNTEuNSA5My44eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(DropboxSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DropboxSquareFilled';
}
export default RefIcon;