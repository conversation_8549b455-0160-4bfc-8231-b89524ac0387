function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DeliveredProcedureOutlinedSvg from "@ant-design/icons-svg/es/asn/DeliveredProcedureOutlined";
import AntdIcon from "../components/AntdIcon";
const DeliveredProcedureOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: DeliveredProcedureOutlinedSvg
}));

/**![delivered-procedure](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02MzIgNjk4LjNsMTQxLjktMTEyYTggOCAwIDAwMC0xMi42TDYzMiA0NjEuN2MtNS4zLTQuMi0xMy0uNC0xMyA2LjN2NzZIMjk1Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDMyNHY3NmMwIDYuNyA3LjggMTAuNCAxMyA2LjN6bTI2MS4zLTQwNUw3MzAuNyAxMzAuN2MtNy41LTcuNS0xNi43LTEzLTI2LjctMTZWMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mjc4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMTg0aDEzNnYxMzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMzIwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIwNS44bDEzNiAxMzZWNDIyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTgzLjVjMC0xNy02LjctMzMuMi0xOC43LTQ1LjJ6TTY0MCAyODhIMzg0VjE4NGgyNTZ2MTA0em0yNjQgNDM2aC01NmMtNC40IDAtOCAzLjYtOCA4djEwOEgxODRWNzMyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHYxNDhjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjczMmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(DeliveredProcedureOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DeliveredProcedureOutlined';
}
export default RefIcon;