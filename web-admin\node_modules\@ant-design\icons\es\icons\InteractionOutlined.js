function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import InteractionOutlinedSvg from "@ant-design/icons-svg/es/asn/InteractionOutlined";
import AntdIcon from "../components/AntdIcon";
const InteractionOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: InteractionOutlinedSvg
}));

/**![interaction](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6TTMwNC44IDUyNGg1MC43YzMuNyAwIDYuOC0zIDYuOC02Ljh2LTc4LjljMC0xOS43IDE1LjktMzUuNiAzNS41LTM1LjZoMjA1Ljd2NTMuNGMwIDUuNyA2LjUgOC44IDEwLjkgNS4zbDEwOS4xLTg1LjdjMy41LTIuNyAzLjUtOCAwLTEwLjdsLTEwOS4xLTg1LjdjLTQuNC0zLjUtMTAuOS0uMy0xMC45IDUuM1YzMzhIMzk3LjdjLTU1LjEgMC05OS43IDQ0LjgtOTkuNyAxMDAuMVY1MTdjMCA0IDMgNyA2LjggN3ptLTQuMiAxMzQuOWwxMDkuMSA4NS43YzQuNCAzLjUgMTAuOS4zIDEwLjktNS4zdi01My40aDIwNS43YzU1LjEgMCA5OS43LTQ0LjggOTkuNy0xMDAuMXYtNzguOWMwLTMuNy0zLTYuOC02LjgtNi44aC01MC43Yy0zLjcgMC02LjggMy02LjggNi44djc4LjljMCAxOS43LTE1LjkgMzUuNi0zNS41IDM1LjZINDIwLjZWNTY4YzAtNS43LTYuNS04LjgtMTAuOS01LjNsLTEwOS4xIDg1LjdjLTMuNSAyLjUtMy41IDcuOCAwIDEwLjV6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(InteractionOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InteractionOutlined';
}
export default RefIcon;