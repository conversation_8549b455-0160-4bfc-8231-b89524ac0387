function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UnderlineOutlinedSvg from "@ant-design/icons-svg/es/asn/UnderlineOutlined";
import AntdIcon from "../components/AntdIcon";
const UnderlineOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: UnderlineOutlinedSvg
}));

/**![underline](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNCA4MDRIMjAwYy00LjQgMC04IDMuNC04IDcuNnY2MC44YzAgNC4yIDMuNiA3LjYgOCA3LjZoNjI0YzQuNCAwIDgtMy40IDgtNy42di02MC44YzAtNC4yLTMuNi03LjYtOC03LjZ6bS0zMTItNzZjNjkuNCAwIDEzNC42LTI3LjEgMTgzLjgtNzYuMkM3NDUgNjAyLjcgNzcyIDUzNy40IDc3MiA0NjhWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA5Ny03OSAxNzYtMTc2IDE3NnMtMTc2LTc5LTE3Ni0xNzZWMTU2YzAtNi42LTUuNC0xMi0xMi0xMmgtNjBjLTYuNiAwLTEyIDUuNC0xMiAxMnYzMTJjMCA2OS40IDI3LjEgMTM0LjYgNzYuMiAxODMuOEMzNzcuMyA3MDEgNDQyLjYgNzI4IDUxMiA3Mjh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(UnderlineOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UnderlineOutlined';
}
export default RefIcon;