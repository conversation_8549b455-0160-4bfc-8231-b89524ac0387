function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SplitCellsOutlinedSvg from "@ant-design/icons-svg/es/asn/SplitCellsOutlined";
import AntdIcon from "../components/AntdIcon";
const SplitCellsOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SplitCellsOutlinedSvg
}));

/**![split-cells](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05MzguMiA1MDguNEw3ODcuMyAzODljLTMtMi40LTcuMy0uMi03LjMgMy42VjQ3OEg2MzZWMTg0aDIwNHYxMjhjMCAyLjIgMS44IDQgNCA0aDYwYzIuMiAwIDQtMS44IDQtNFYxNDRjMC0xNS41LTEyLjUtMjgtMjgtMjhINTk2Yy0xNS41IDAtMjggMTIuNS0yOCAyOHY3MzZjMCAxNS41IDEyLjUgMjggMjggMjhoMjg0YzE1LjUgMCAyOC0xMi41IDI4LTI4VjcxMmMwLTIuMi0xLjgtNC00LTRoLTYwYy0yLjIgMC00IDEuOC00IDR2MTI4SDYzNlY1NDZoMTQ0djg1LjRjMCAzLjggNC40IDYgNy4zIDMuNmwxNTAuOS0xMTkuNGE0LjUgNC41IDAgMDAwLTcuMnpNNDI4IDExNkgxNDRjLTE1LjUgMC0yOCAxMi41LTI4IDI4djE2OGMwIDIuMiAxLjggNCA0IDRoNjBjMi4yIDAgNC0xLjggNC00VjE4NGgyMDR2Mjk0SDI0NHYtODUuNGMwLTMuOC00LjMtNi03LjMtMy42bC0xNTEgMTE5LjRhNC41MiA0LjUyIDAgMDAwIDcuMWwxNTEgMTE5LjVjMi45IDIuMyA3LjMuMiA3LjMtMy42VjU0NmgxNDR2Mjk0SDE4NFY3MTJjMC0yLjItMS44LTQtNC00aC02MGMtMi4yIDAtNCAxLjgtNCA0djE2OGMwIDE1LjUgMTIuNSAyOCAyOCAyOGgyODRjMTUuNSAwIDI4LTEyLjUgMjgtMjhWMTQ0YzAtMTUuNS0xMi41LTI4LTI4LTI4eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(SplitCellsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SplitCellsOutlined';
}
export default RefIcon;