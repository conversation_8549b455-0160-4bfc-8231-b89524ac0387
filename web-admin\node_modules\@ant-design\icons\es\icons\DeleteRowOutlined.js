function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DeleteRowOutlinedSvg from "@ant-design/icons-svg/es/asn/DeleteRowOutlined";
import AntdIcon from "../components/AntdIcon";
const DeleteRowOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: DeleteRowOutlinedSvg
}));

/**![delete-row](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04MTkuOCA1MTJsMTAyLjQtMTIyLjlhOC4wNiA4LjA2IDAgMDAtNi4xLTEzLjJoLTU0LjdjLTIuNCAwLTQuNiAxLjEtNi4xIDIuOUw3ODIgNDY2LjdsLTczLjEtODcuOGE4LjEgOC4xIDAgMDAtNi4xLTIuOUg2NDhjLTEuOSAwLTMuNy43LTUuMSAxLjlhNy45NyA3Ljk3IDAgMDAtMSAxMS4zTDc0NC4yIDUxMiA2NDEuOCA2MzQuOWE4LjA2IDguMDYgMCAwMDYuMSAxMy4yaDU0LjdjMi40IDAgNC42LTEuMSA2LjEtMi45bDczLjEtODcuOCA3My4xIDg3LjhhOC4xIDguMSAwIDAwNi4xIDIuOWg1NWMxLjkgMCAzLjctLjcgNS4xLTEuOSAzLjQtMi44IDMuOS03LjkgMS0xMS4zTDgxOS44IDUxMnpNNTM2IDQ2NEgxMjBjLTQuNCAwLTggMy42LTggOHY4MGMwIDQuNCAzLjYgOCA4IDhoNDE2YzQuNCAwIDgtMy42IDgtOHYtODBjMC00LjQtMy42LTgtOC04em0tODQgMjA0aC02MGMtMy4zIDAtNiAyLjctNiA2djE2NkgxMzZjLTMuMyAwLTYgMi43LTYgNnY2MGMwIDMuMyAyLjcgNiA2IDZoMjkyYzE2LjYgMCAzMC0xMy40IDMwLTMwVjY3NGMwLTMuMy0yLjctNi02LTZ6TTEzNiAxODRoMjUwdjE2NmMwIDMuMyAyLjcgNiA2IDZoNjBjMy4zIDAgNi0yLjcgNi02VjE0MmMwLTE2LjYtMTMuNC0zMC0zMC0zMEgxMzZjLTMuMyAwLTYgMi43LTYgNnY2MGMwIDMuMyAyLjcgNiA2IDZ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(DeleteRowOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DeleteRowOutlined';
}
export default RefIcon;