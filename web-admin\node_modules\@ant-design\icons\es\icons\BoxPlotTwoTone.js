function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BoxPlotTwoToneSvg from "@ant-design/icons-svg/es/asn/BoxPlotTwoTone";
import AntdIcon from "../components/AntdIcon";
const BoxPlotTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: BoxPlotTwoToneSvg
}));

/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAzNjhoODh2Mjg4aC04OHptMTUyIDBoMjgwdjI4OEg0NDh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05NTIgMjI0aC01MmMtNC40IDAtOCAzLjYtOCA4djI0OGgtOTJWMzA0YzAtNC40LTMuNi04LTgtOEgyMzJjLTQuNCAwLTggMy42LTggOHYxNzZoLTkyVjIzMmMwLTQuNC0zLjYtOC04LThINzJjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFY1NDhoOTJ2MTcyYzAgNC40IDMuNiA4IDggOGg1NjBjNC40IDAgOC0zLjYgOC04VjU0OGg5MnYyNDRjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFYyMzJjMC00LjQtMy42LTgtOC04ek0zODQgNjU2aC04OFYzNjhoODh2Mjg4em0zNDQgMEg0NDhWMzY4aDI4MHYyODh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(BoxPlotTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoxPlotTwoTone';
}
export default RefIcon;