function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BoxPlotFilledSvg from "@ant-design/icons-svg/es/asn/BoxPlotFilled";
import AntdIcon from "../components/AntdIcon";
const BoxPlotFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: BoxPlotFilledSvg
}));

/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiAyMjRoLTUyYy00LjQgMC04IDMuNi04IDh2MjQ4aC05MlYzMDRjMC00LjQtMy42LTgtOC04SDQ0OHY0MzJoMzQ0YzQuNCAwIDgtMy42IDgtOFY1NDhoOTJ2MjQ0YzAgNC40IDMuNiA4IDggOGg1MmM0LjQgMCA4LTMuNiA4LThWMjMyYzAtNC40LTMuNi04LTgtOHptLTcyOCA4MHYxNzZoLTkyVjIzMmMwLTQuNC0zLjYtOC04LThINzJjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFY1NDhoOTJ2MTcyYzAgNC40IDMuNiA4IDggOGgxNTJWMjk2SDIzMmMtNC40IDAtOCAzLjYtOCA4eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(BoxPlotFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoxPlotFilled';
}
export default RefIcon;