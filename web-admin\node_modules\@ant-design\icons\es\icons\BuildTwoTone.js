function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BuildTwoToneSvg from "@ant-design/icons-svg/es/asn/BuildTwoTone";
import AntdIcon from "../components/AntdIcon";
const BuildTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: BuildTwoToneSvg
}));

/**![build](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NCA1NDZoMjAwdjIwMEgxNDR6bTI2OC0yNjhoMjAwdjIwMEg0MTJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MTYgMjEwSDM3NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjM2SDEwOGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjcyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDU0MGMxNy43IDAgMzItMTQuMyAzMi0zMlY1NDZoMjM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjI0MmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzQ0IDc0NkgxNDRWNTQ2aDIwMHYyMDB6bTI2OCAwSDQxMlY1NDZoMjAwdjIwMHptMC0yNjhINDEyVjI3OGgyMDB2MjAwem0yNjggMEg2ODBWMjc4aDIwMHYyMDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(BuildTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BuildTwoTone';
}
export default RefIcon;