function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MediumCircleFilledSvg from "@ant-design/icons-svg/es/asn/MediumCircleFilled";
import AntdIcon from "../components/AntdIcon";
const MediumCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: MediumCircleFilledSvg
}));

/**![medium-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNTYgMjUzLjdsLTQwLjggMzkuMWMtMy42IDIuNy01LjMgNy4xLTQuNiAxMS40djI4Ny43Yy0uNyA0LjQgMSA4LjggNC42IDExLjRsNDAgMzkuMXY4LjdINTY2LjR2LTguM2w0MS4zLTQwLjFjNC4xLTQuMSA0LjEtNS4zIDQuMS0xMS40VjQyMi41bC0xMTUgMjkxLjZoLTE1LjVMMzQ3LjUgNDIyLjVWNjE4Yy0xLjIgOC4yIDEuNyAxNi41IDcuNSAyMi40bDUzLjggNjUuMXY4LjdIMjU2di04LjdsNTMuOC02NS4xYTI2LjEgMjYuMSAwIDAwNy0yMi40VjM5MmMuNy02LjMtMS43LTEyLjQtNi41LTE2LjdsLTQ3LjgtNTcuNlYzMDlINDExbDExNC42IDI1MS41IDEwMC45LTI1MS4zSDc2OHY4LjV6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(MediumCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MediumCircleFilled';
}
export default RefIcon;