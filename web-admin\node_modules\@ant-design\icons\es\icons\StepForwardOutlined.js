function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import StepForwardOutlinedSvg from "@ant-design/icons-svg/es/asn/StepForwardOutlined";
import AntdIcon from "../components/AntdIcon";
const StepForwardOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: StepForwardOutlinedSvg
}));

/**![step-forward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3Ni40IDUyOC45NUwyOTMuMiA4MjkuOTdjLTE0LjI1IDExLjItMzUuMiAxLjEtMzUuMi0xNi45NVYyMTAuOTdjMC0xOC4wNSAyMC45NS0yOC4xNCAzNS4yLTE2Ljk0bDM4My4yIDMwMS4wMmEyMS41MyAyMS41MyAwIDAxMCAzMy45TTY5NCA4NjRoNjRhOCA4IDAgMDA4LThWMTY4YTggOCAwIDAwLTgtOGgtNjRhOCA4IDAgMDAtOCA4djY4OGE4IDggMCAwMDggOCIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(StepForwardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StepForwardOutlined';
}
export default RefIcon;