function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SkinOutlinedSvg from "@ant-design/icons-svg/es/asn/SkinOutlined";
import AntdIcon from "../components/AntdIcon";
const SkinOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SkinOutlinedSvg
}));

/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MCAxMjZINjYzLjhjLTE3LjQgMC0zMi45IDExLjktMzcgMjkuM0M2MTQuMyAyMDguMSA1NjcgMjQ2IDUxMiAyNDZzLTEwMi4zLTM3LjktMTE0LjgtOTAuN2EzNy45MyAzNy45MyAwIDAwLTM3LTI5LjNIMTU0YTQ0IDQ0IDAgMDAtNDQgNDR2MjUyYTQ0IDQ0IDAgMDA0NCA0NGg3NXYzODhhNDQgNDQgMCAwMDQ0IDQ0aDQ3OGE0NCA0NCAwIDAwNDQtNDRWNDY2aDc1YTQ0IDQ0IDAgMDA0NC00NFYxNzBhNDQgNDQgMCAwMC00NC00NHptLTI4IDI2OEg3MjN2NDMySDMwMVYzOTRIMTgyVjE5OGgxNTMuM2MyOC4yIDcxLjIgOTcuNSAxMjAgMTc2LjcgMTIwczE0OC41LTQ4LjggMTc2LjctMTIwSDg0MnYxOTZ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(SkinOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SkinOutlined';
}
export default RefIcon;