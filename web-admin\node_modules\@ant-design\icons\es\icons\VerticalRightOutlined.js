function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import VerticalRightOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalRightOutlined";
import AntdIcon from "../components/AntdIcon";
const VerticalRightOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: VerticalRightOutlinedSvg
}));

/**![vertical-right](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyNiAxNjRoLTY0Yy00LjQgMC04IDMuNi04IDh2Njg4YzAgNC40IDMuNiA4IDggOGg2NGM0LjQgMCA4LTMuNiA4LThWMTcyYzAtNC40LTMuNi04LTgtOHptNDQ0IDcyLjRWMTY0YzAtNi44LTcuOS0xMC41LTEzLjEtNi4xTDMzNSA1MTJsNDIxLjkgMzU0LjFjNS4yIDQuNCAxMy4xLjcgMTMuMS02LjF2LTcyLjRjMC05LjQtNC4yLTE4LjQtMTEuNC0yNC41TDQ1OS40IDUxMmwyOTkuMi0yNTEuMWM3LjItNi4xIDExLjQtMTUuMSAxMS40LTI0LjV6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(VerticalRightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VerticalRightOutlined';
}
export default RefIcon;