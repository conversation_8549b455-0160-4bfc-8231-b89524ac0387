function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RightSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/RightSquareOutlined";
import AntdIcon from "../components/AntdIcon";
const RightSquareOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: RightSquareOutlinedSvg
}));

/**![right-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQxMi43IDY5Ni41bDI0Ni0xNzhjNC40LTMuMiA0LjQtOS43IDAtMTIuOWwtMjQ2LTE3OGMtNS4zLTMuOC0xMi43IDAtMTIuNyA2LjVWMzgxYzAgMTAuMiA0LjkgMTkuOSAxMy4yIDI1LjlMNTU4LjYgNTEyIDQxMy4yIDYxNy4yYy04LjMgNi0xMy4yIDE1LjYtMTMuMiAyNS45VjY5MGMwIDYuNSA3LjQgMTAuMyAxMi43IDYuNXoiIC8+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(RightSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightSquareOutlined';
}
export default RefIcon;