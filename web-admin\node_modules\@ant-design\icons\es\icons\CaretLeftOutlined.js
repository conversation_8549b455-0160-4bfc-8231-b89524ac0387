function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CaretLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/CaretLeftOutlined";
import AntdIcon from "../components/AntdIcon";
const CaretLeftOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: CaretLeftOutlinedSvg
}));

/**![caret-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OSAxNjUuMUwzMDguMiA0OTMuNWMtMTAuOSA5LjQtMTAuOSAyNy41IDAgMzdMNjg5IDg1OC45YzE0LjIgMTIuMiAzNSAxLjIgMzUtMTguNVYxODMuNmMwLTE5LjctMjAuOC0zMC43LTM1LTE4LjV6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(CaretLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CaretLeftOutlined';
}
export default RefIcon;