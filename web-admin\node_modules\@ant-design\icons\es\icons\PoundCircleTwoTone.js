function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PoundCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PoundCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
const PoundCircleTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PoundCircleTwoToneSvg
}));

/**![pound-circle](data:image/svg+xml;base64,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) */
const RefIcon = /*#__PURE__*/React.forwardRef(PoundCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PoundCircleTwoTone';
}
export default RefIcon;