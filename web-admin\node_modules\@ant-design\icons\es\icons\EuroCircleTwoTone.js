function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import EuroCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/EuroCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
const EuroCircleTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: EuroCircleTwoToneSvg
}));

/**![euro-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTExNy4xIDU4MS4xYzAgMy44LTIuNyA3LTYuNCA3LjgtMTUuOSAzLjQtMzQuNCA1LjEtNTUuMyA1LjEtMTA5LjggMC0xODMtNTguOC0yMDAuMi0xNThIMzM3Yy00LjQgMC04LTMuNi04LTh2LTI3LjJjMC00LjQgMy42LTggOC04aDI2LjF2LTM2LjljMC00LjQgMC04LjcuMy0xMi44SDMzN2MtNC40IDAtOC0zLjYtOC04di0yNy4yYzAtNC40IDMuNi04IDgtOGgzMS44QzM4OC41IDM0NS43IDQ2MC43IDI5MCA1NjcuNCAyOTBjMjAuOSAwIDM5LjQgMS45IDU1LjMgNS40IDMuNy44IDYuMyA0IDYuMyA3LjhWMzQ2YTggOCAwIDAxLTkuNiA3LjhjLTE0LjYtMi45LTMxLjgtNC40LTUxLjctNC40LTY1LjMgMC0xMTAuNCAzMy41LTEyNy42IDkwLjRoMTI4LjNjNC40IDAgOCAzLjYgOCA4VjQ3NWMwIDQuNC0zLjYgOC04IDhINDMyLjVjLS4zIDQuNC0uMyA5LjEtLjMgMTMuOHYzNmgxMzYuNGM0LjQgMCA4IDMuNiA4IDhWNTY4YzAgNC40LTMuNiA4LTggOEg0MzhjMTUuMyA2MiA2MS4zIDk4LjYgMTI5LjggOTguNiAxOS45IDAgMzcuMS0xLjMgNTEuOC00LjEgNC45LTEgOS41IDIuOCA5LjUgNy44djQyLjh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02MTkuNiA2NzAuNWMtMTQuNyAyLjgtMzEuOSA0LjEtNTEuOCA0LjEtNjguNSAwLTExNC41LTM2LjYtMTI5LjgtOTguNmgxMzAuNmM0LjQgMCA4LTMuNiA4LTh2LTI3LjJjMC00LjQtMy42LTgtOC04SDQzMi4ydi0zNmMwLTQuNyAwLTkuNC4zLTEzLjhoMTM1LjljNC40IDAgOC0zLjYgOC04di0yNy4yYzAtNC40LTMuNi04LTgtOEg0NDAuMWMxNy4yLTU2LjkgNjIuMy05MC40IDEyNy42LTkwLjQgMTkuOSAwIDM3LjEgMS41IDUxLjcgNC40YTggOCAwIDAwOS42LTcuOHYtNDIuOGMwLTMuOC0yLjYtNy02LjMtNy44LTE1LjktMy41LTM0LjQtNS40LTU1LjMtNS40LTEwNi43IDAtMTc4LjkgNTUuNy0xOTguNiAxNDkuOUgzMzdjLTQuNCAwLTggMy42LTggOHYyNy4yYzAgNC40IDMuNiA4IDggOGgyNi40Yy0uMyA0LjEtLjMgOC40LS4zIDEyLjh2MzYuOUgzMzdjLTQuNCAwLTggMy42LTggOFY1NjhjMCA0LjQgMy42IDggOCA4aDMwLjJjMTcuMiA5OS4yIDkwLjQgMTU4IDIwMC4yIDE1OCAyMC45IDAgMzkuNC0xLjcgNTUuMy01LjEgMy43LS44IDYuNC00IDYuNC03Ljh2LTQyLjhjMC01LTQuNi04LjgtOS41LTcuOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(EuroCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'EuroCircleTwoTone';
}
export default RefIcon;