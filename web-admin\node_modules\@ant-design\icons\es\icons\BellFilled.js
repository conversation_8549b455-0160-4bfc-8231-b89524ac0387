function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BellFilledSvg from "@ant-design/icons-svg/es/asn/BellFilled";
import AntdIcon from "../components/AntdIcon";
const BellFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: BellFilledSvg
}));

/**![bell](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgxNiA3NjhoLTI0VjQyOGMwLTE0MS4xLTEwNC4zLTI1Ny44LTI0MC0yNzcuMlYxMTJjMC0yMi4xLTE3LjktNDAtNDAtNDBzLTQwIDE3LjktNDAgNDB2MzguOEMzMzYuMyAxNzAuMiAyMzIgMjg2LjkgMjMyIDQyOHYzNDBoLTI0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoMjE2YzAgNjEuOCA1MC4yIDExMiAxMTIgMTEyczExMi01MC4yIDExMi0xMTJoMjE2YzQuNCAwIDgtMy42IDgtOHYtMzJjMC0xNy43LTE0LjMtMzItMzItMzJ6TTUxMiA4ODhjLTI2LjUgMC00OC0yMS41LTQ4LTQ4aDk2YzAgMjYuNS0yMS41IDQ4LTQ4IDQ4eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(BellFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BellFilled';
}
export default RefIcon;