function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PieChartTwoToneSvg from "@ant-design/icons-svg/es/asn/PieChartTwoTone";
import AntdIcon from "../components/AntdIcon";
const PieChartTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PieChartTwoToneSvg
}));

/**![pie-chart](data:image/svg+xml;base64,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) */
const RefIcon = /*#__PURE__*/React.forwardRef(PieChartTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PieChartTwoTone';
}
export default RefIcon;